{"name": "store-manager", "version": "1.0.0", "private": true, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "scripts": {"development": "npm install && npm run format && npm run lint && next dev", "stock-queue": "ts-node lib/worker/stock.ts", "dev": "concurrently \"npm:development -- --port 3000\" \"npm:stock-queue\"", "build": "next build", "start": "next start", "format": "prettier --check --write \"{app,demo,layout,types}/**/*.{js,ts,tsx,d.ts}\"", "lint": "next lint"}, "dependencies": {"@types/node": "^24.0.10", "@types/react": "18.2.12", "@types/react-dom": "18.2.5", "@vercel/analytics": "^1.5.0", "bufferutil": "^4.0.9", "bullmq": "^5.56.1", "canvas": "^3.1.2", "chart.js": "4.2.1", "cloudinary": "^2.6.1", "concurrently": "^9.2.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "ioredis": "^5.6.1", "isomorphic-dompurify": "^2.25.0", "lodash": "^4.17.21", "mongoose": "^8.14.0", "next": "13.5.9", "next-auth": "^4.24.11", "next-pwa": "^5.6.0", "primeflex": "^3.3.1", "primeicons": "^6.0.1", "primereact": "10.2.1", "react": "18.2.0", "react-dom": "18.2.0", "typescript": "^5.8.3", "utf-8-validate": "^6.0.5", "uuid": "^11.1.0", "valibot": "^1.1.0", "zustand": "^5.0.4"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/lodash": "^4.17.16", "@types/mongoose": "^5.11.97", "@types/next-pwa": "^5.6.9", "@types/react-transition-group": "^4.4.12", "eslint": "8.43.0", "eslint-config-next": "13.4.6", "prettier": "^2.8.8", "sass": "^1.63.4"}}