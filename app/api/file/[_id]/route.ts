import handshakeDB from '@/lib/mongo';
import fileSchema, { FileDocument } from '@/models/file.schema';
import { NextRequest } from 'next/server';

export async function GET(_: NextRequest, { params }: { params: Promise<{ _id: string }> }) {
    let response: Response;

    try {
        await handshakeDB();
        const { _id } = await params;
        const file = await fileSchema.findOne({ _id }).select('-__v').lean<FileDocument>();

        if (!file) {
            response = new Response('File not found!', { status: 404 });
        } else {
            response = new Response(Buffer.from(file.data.toString('base64'), 'base64'), { status: 200, headers: { 'Content-Type': file.type, 'Content-Disposition': `inline; filename="${file.name}"` } });
        }
    } catch (error) {
        console.error(error);
        response = new Response((error as any)?.message ?? 'File not found!', { status: 404 });
    }

    return response;
}
