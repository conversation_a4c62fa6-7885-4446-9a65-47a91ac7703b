import { syncStock } from '@/mutations/item/inventory/stock/sync';
import { Queue, Worker } from 'bullmq';
import redis from '../redis';

const queue = 'stockQueue';

const stockWorker = new Worker(
    queue,
    async () => {
        console.info('sync stock worker running ...');
        await syncStock();
        console.info('sync stock worker completed ...');
    },
    { connection: redis, concurrency: 3, removeOnComplete: { age: 60 }, removeOnFail: { age: 60 } }
);

export const stockQueue = new Queue(queue, { connection: redis, defaultJobOptions: { attempts: 3, backoff: { type: 'exponential', delay: 2100 }, removeOnComplete: true, removeOnFail: true } });

export default stockWorker;
