# Store Manager

Produk digital untuk UMKM yang berupa sistem Grosir dan <PERSON>

## SonarQube Community Build

Sonar Scanner Config

-   [guide ref](https://docs.sonarsource.com/sonarqube-community-build/try-out-sonarqube/)
-   add in package.json or run in console/terminal

    `sonar -Dsonar.host.url=http://localhost:9000 -Dsonar.token=sqp_2c771e2a8bf4f8cf0b4c631ce8925edace944808 -Dsonar.projectKey=Store-Manager`

-   akses:
    -   user: `admin`
    -   password: `r28Cwtl8R1fisfegxHBZkqDoIEYFJyT8ISQ5WENt8pJ7J$`

## Temporary Notes

Buat cleansing sampah hasil import dari sqlite 2024

`db.products.deleteMany({name:{$in:[",",",,",",,,",",,,,",",,,,,",",,,,,,",",,,,,,,",",,,,,,,,",",,,,,,,,,",",,,,,,,,,,",",,,,,,,,,,,",",,,,,,,,,,,,",",,,,,,,,,,,,,",",.",",..",".","..","...","....",".....","......",".......","........",".........","..........","...........","............",".,","???","/","/...........","////////"]}});`
